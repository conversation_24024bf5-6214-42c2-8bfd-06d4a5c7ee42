#!/bin/bash
set -e

echo "🔍 Environment debugging information..."
echo "Current user: $(whoami)"
echo "User ID: $(id)"
echo "Operating System:"
if [ -f /etc/os-release ]; then
    cat /etc/os-release
else
    uname -a
fi
echo "Available package managers:"
command -v apk && echo "✅ apk available" || echo "❌ apk not available"
command -v apt-get && echo "✅ apt-get available" || echo "❌ apt-get not available"
command -v yum && echo "✅ yum available" || echo "❌ yum not available"
command -v dnf && echo "✅ dnf available" || echo "❌ dnf not available"
echo "Pre-installed tools:"
command -v terraform && echo "✅ terraform available at $(which terraform)" || echo "❌ terraform not in PATH"
command -v gitlab-terraform && echo "✅ gitlab-terraform available at $(which gitlab-terraform)" || echo "❌ gitlab-terraform not in PATH"
command -v aws && echo "✅ aws available at $(which aws)" || echo "❌ aws not in PATH"
echo "PATH: $PATH"

echo "🔧 Installing AWS CLI..."

# Detect OS and install AWS CLI appropriately
if [ -f /etc/os-release ]; then
    . /etc/os-release
    echo "Detected OS: $NAME $VERSION_ID"
fi

# Check if we have root privileges
if [ "$(id -u)" -eq 0 ]; then
    echo "Running as root - can install packages"
    HAS_ROOT=true
else
    echo "Running as non-root user - will try user-space installation"
    HAS_ROOT=false
fi

# Try different installation methods based on available package managers and permissions
if command -v apk >/dev/null 2>&1 && [ "$HAS_ROOT" = true ]; then
    echo "Installing AWS CLI via apk (Alpine Linux)..."
    apk add --no-cache aws-cli
elif command -v apt-get >/dev/null 2>&1 && [ "$HAS_ROOT" = true ]; then
    echo "Installing AWS CLI via apt (Debian/Ubuntu)..."
    apt-get update && apt-get install -y awscli
elif command -v yum >/dev/null 2>&1 && [ "$HAS_ROOT" = true ]; then
    echo "Installing AWS CLI via yum (RHEL/CentOS)..."
    yum install -y awscli
elif command -v dnf >/dev/null 2>&1 && [ "$HAS_ROOT" = true ]; then
    echo "Installing AWS CLI via dnf (Fedora)..."
    dnf install -y awscli
else
    echo "Package manager installation not available, trying manual installation..."
    
    # Manual installation to user directory (works without root)
    echo "Installing AWS CLI v2 manually to user directory..."
    
    # Install dependencies if possible
    if command -v apt-get >/dev/null 2>&1 && [ "$HAS_ROOT" = true ]; then
        apt-get update && apt-get install -y curl unzip
    elif command -v apk >/dev/null 2>&1 && [ "$HAS_ROOT" = true ]; then
        apk add --no-cache curl unzip
    fi
    
    # Create user directories
    mkdir -p $HOME/aws-cli-bin
    mkdir -p $HOME/aws-cli
    
    # Detect architecture
    ARCH=$(uname -m)
    if [ "$ARCH" = "x86_64" ]; then
        AWS_CLI_URL="https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip"
    elif [ "$ARCH" = "aarch64" ]; then
        AWS_CLI_URL="https://awscli.amazonaws.com/awscli-exe-linux-aarch64.zip"
    else
        echo "❌ Unsupported architecture: $ARCH"
        exit 1
    fi
    
    # Download and install
    curl "$AWS_CLI_URL" -o "awscliv2.zip"
    unzip awscliv2.zip
    ./aws/install --install-dir $HOME/aws-cli --bin-dir $HOME/aws-cli-bin
    
    # Add to PATH
    export PATH="$HOME/aws-cli-bin:$PATH"
    echo 'export PATH="$HOME/aws-cli-bin:$PATH"' >> $HOME/.bashrc
    
    # Clean up
    rm -rf awscliv2.zip aws/
fi

echo "✅ AWS CLI installation completed"

echo "🔍 Verifying tool installations..."

# Ensure AWS CLI is in PATH if installed manually
if [ -d "$HOME/aws-cli-bin" ]; then
    export PATH="$HOME/aws-cli-bin:$PATH"
fi

# Check for terraform in various locations
echo "Checking for Terraform..."
if command -v terraform >/dev/null 2>&1; then
    echo "✅ Terraform found in PATH"
    echo "Terraform version:"
    terraform --version
elif command -v gitlab-terraform >/dev/null 2>&1; then
    echo "✅ gitlab-terraform found, creating terraform alias"
    echo "gitlab-terraform version:"
    gitlab-terraform --version
    # Create terraform alias for compatibility
    ln -sf $(which gitlab-terraform) /usr/local/bin/terraform 2>/dev/null || echo "Note: Could not create terraform symlink"
elif [ -f /usr/bin/terraform ]; then
    echo "✅ Terraform found at /usr/bin/terraform"
    echo "Terraform version:"
    /usr/bin/terraform --version
elif [ -f /usr/local/bin/terraform ]; then
    echo "✅ Terraform found at /usr/local/bin/terraform"
    echo "Terraform version:"
    /usr/local/bin/terraform --version
else
    echo "❌ Terraform not found in any expected location"
    echo "Searching for terraform binaries..."
    find /usr -name "*terraform*" 2>/dev/null || echo "No terraform binaries found"
    echo "Available commands:"
    compgen -c | grep -i terraform || echo "No terraform commands found"
    echo "⚠️  Terraform not available in this image"
fi

# Check AWS CLI
echo "Checking for AWS CLI..."
if command -v aws >/dev/null 2>&1; then
    echo "✅ AWS CLI found"
    echo "AWS CLI version:"
    aws --version
else
    echo "❌ AWS CLI not found"
    exit 1
fi

echo "✅ Tool verification completed"
