{"name": "aws-ecs-demo", "version": "1.2.0-dev.1", "description": "NestJS Hello World application for AWS ECS demo", "main": "dist/main.js", "repository": "https://gitlab.uastage.com/sw-web/aws-ecs-demo.git", "author": "serhii.poliarush <<EMAIL>>", "license": "MIT", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "semantic-release": "semantic-release"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/serve-static": "^4.0.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@types/express": "^4.17.17", "@types/node": "^18.15.0", "@typescript-eslint/eslint-plugin": "^5.57.0", "@typescript-eslint/parser": "^5.57.0", "eslint": "^8.37.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "prettier": "^2.8.7", "semantic-release": "^20.1.0", "source-map-support": "^0.5.21", "ts-loader": "^9.4.2", "ts-node": "^10.9.1", "tsconfig-paths": "^4.1.2", "typescript": "^4.9.5"}, "engines": {"node": ">=18.0.0"}}