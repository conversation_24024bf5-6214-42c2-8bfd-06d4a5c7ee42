#!/bin/sh
set -e

echo "🔍 Validating required CI/CD variables..."
# Simulate environment variables (in real CI these would be set)
export AWS_ACCESS_KEY_ID="test-key"
export AWS_SECRET_ACCESS_KEY="test-secret"
export AWS_DEFAULT_REGION="us-east-1"
export CI_REGISTRY_USER="test-user"
export CI_REGISTRY_PASSWORD="test-password"
export CI_PROJECT_ID="123"
export CI_JOB_TOKEN="test-token"
export CI_SERVER_URL="https://gitlab.example.com"

# Check AWS credentials
if [ -z "$AWS_ACCESS_KEY_ID" ] || [ -z "$AWS_SECRET_ACCESS_KEY" ]; then
  echo "❌ Error: AWS credentials not configured"
  exit 1
fi

# Check AWS region (use default if not set)
if [ -z "$AWS_DEFAULT_REGION" ]; then
  export AWS_DEFAULT_REGION="us-east-1"
  echo "⚠️  AWS_DEFAULT_REGION not set, using default: us-east-1"
fi

# Check GitLab registry credentials
if [ -z "$CI_REGISTRY_USER" ] || [ -z "$CI_REGISTRY_PASSWORD" ]; then
  echo "❌ Error: GitLab registry credentials not available"
  exit 1
fi

# Check GitLab CI built-in variables
if [ -z "$CI_PROJECT_ID" ] || [ -z "$CI_JOB_TOKEN" ] || [ -z "$CI_SERVER_URL" ]; then
  echo "❌ Error: Required GitLab CI built-in variables are missing"
  exit 1
fi

echo "✅ All required variables validated"

echo "🔍 Verifying pre-installed tools in zenika/terraform-aws-cli image..."

# Display environment information
echo "Container OS: $(cat /etc/os-release | grep PRETTY_NAME | cut -d'"' -f2)"
echo "Architecture: $(uname -m)"
echo "Current user: $(whoami)"

# Verify both tools are available and working
echo ""
echo "Terraform version:"
terraform --version
echo ""
echo "AWS CLI version:"
aws --version
echo ""
echo "Additional tools:"
git --version
jq --version
echo ""
echo "✅ All tools verified and ready to use"

echo "✅ AWS credentials configured for region: $AWS_DEFAULT_REGION"
echo "✅ Ready to execute Terraform commands"

echo ""
echo "🧪 Testing basic Terraform functionality..."
# Create a simple test terraform file
mkdir -p /tmp/terraform-test
cd /tmp/terraform-test

cat > main.tf << 'TERRAFORM'
terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

provider "aws" {
  region = var.aws_region
}

variable "aws_region" {
  description = "AWS region"
  type        = string
  default     = "us-east-1"
}

output "test_output" {
  value = "Terraform is working correctly!"
}
TERRAFORM

echo "✅ Created test Terraform configuration"

# Test terraform init (this will work even without real AWS credentials)
echo "🔧 Testing terraform init..."
terraform init
echo "✅ terraform init completed successfully"

# Test terraform validate
echo "🔧 Testing terraform validate..."
terraform validate
echo "✅ terraform validate completed successfully"

# Test terraform plan (this will fail without real AWS credentials, but that's expected)
echo "🔧 Testing terraform plan (expected to fail without real AWS credentials)..."
if terraform plan -out=tfplan 2>/dev/null; then
  echo "✅ terraform plan completed successfully (unexpected but good!)"
else
  echo "⚠️  terraform plan failed as expected (needs real AWS credentials)"
fi

echo ""
echo "🎉 zenika/terraform-aws-cli image test completed successfully!"
echo "✅ Image is ready for GitLab CI/CD usage"
