#!/usr/bin/env bash
set -e

# Local Terraform Plan Script
# This script allows you to run terraform plan locally for testing

ENVIRONMENT=${1:-development}
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Validate environment
if [[ "$ENVIRONMENT" != "development" && "$ENVIRONMENT" != "production" ]]; then
    echo "❌ Error: Environment must be 'development' or 'production'"
    echo "Usage: $0 [development|production]"
    exit 1
fi

echo "🔍 Running Terraform plan for $ENVIRONMENT environment..."

# Check if AWS credentials are configured
if [[ -z "$AWS_ACCESS_KEY_ID" || -z "$AWS_SECRET_ACCESS_KEY" ]]; then
    echo "❌ Error: AWS credentials not configured"
    echo "   Please set AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY environment variables"
    echo "   Or configure AWS CLI with 'aws configure'"
    exit 1
fi

# Set default AWS region if not set
if [[ -z "$AWS_DEFAULT_REGION" ]]; then
    export AWS_DEFAULT_REGION="us-east-1"
    echo "⚠️  AWS_DEFAULT_REGION not set, using default: us-east-1"
fi

# Navigate to environment directory
cd "$SCRIPT_DIR/environments/$ENVIRONMENT"

# Check if terraform.tfvars exists
if [[ ! -f "terraform.tfvars" ]]; then
    echo "❌ Error: terraform.tfvars not found"
    echo "   Please copy terraform.tfvars.example to terraform.tfvars and fill in your values:"
    echo "   cp terraform.tfvars.example terraform.tfvars"
    echo "   # Edit terraform.tfvars with your values"
    exit 1
fi

echo "✅ Found terraform.tfvars"

# Initialize Terraform (local backend)
echo "🔧 Initializing Terraform..."
terraform init

# Validate configuration
echo "🔍 Validating Terraform configuration..."
terraform validate

# Plan infrastructure changes
echo "📋 Planning infrastructure changes..."
terraform plan

echo "✅ Terraform plan completed successfully!"
echo ""
echo "📝 Next steps:"
echo "   - Review the plan output above"
echo "   - If you want to apply changes locally: terraform apply"
echo "   - For CI/CD deployment, push your changes to GitLab"
