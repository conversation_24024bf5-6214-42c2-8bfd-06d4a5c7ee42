# Production Environment Configuration
terraform {
  required_version = ">= 1.0"

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }

}

terraform {
  # GitLab-managed Terraform state for development
  backend "http" {
    # Configuration will be provided via CLI during terraform init
  }
}

# Local backend for development environment
# terraform {
#   backend "local" {
#     path = "./terraform-dev.tfstate"
#   }
# }

# Use the main Terraform configuration
module "infrastructure" {
  source = "../.."

  # Environment Configuration
  project_name = "aws-ecs-demo"
  environment  = "production"

  # AWS Configuration
  aws_region = "us-east-1"

  # Container Configuration
  container_image  = var.container_image
  container_port   = 3000
  container_cpu    = 512  # Higher CPU for production
  container_memory = 1024 # Higher memory for production

  # ECS Configuration
  desired_count = 2  # Multiple instances for production

  # GitLab Registry Authentication
  gitlab_registry_user     = var.gitlab_registry_user
  gitlab_registry_password = var.gitlab_registry_password

  # Repository Information
  repository_url = var.repository_url
}

# Production-specific variables
variable "container_image" {
  description = "Docker container image URI for production"
  type        = string
}

variable "gitlab_registry_user" {
  description = "GitLab registry username"
  type        = string
}

variable "gitlab_registry_password" {
  description = "GitLab registry password"
  type        = string
  sensitive   = true
}

variable "repository_url" {
  description = "URL of the Git repository (for tagging resources)"
  type        = string
  default     = ""
}

# Production-specific outputs
output "application_url" {
  description = "Production application URL"
  value       = module.infrastructure.application_url
}

output "deployed_image" {
  description = "Container image deployed to production"
  value       = module.infrastructure.deployed_image
}

output "ecs_cluster_name" {
  description = "Production ECS cluster name"
  value       = module.infrastructure.ecs_cluster_name
}

output "ecs_service_name" {
  description = "Production ECS service name"
  value       = module.infrastructure.ecs_service_name
}

output "gitlab_registry_secret_arn" {
  description = "GitLab registry authentication secret ARN"
  value       = module.infrastructure.gitlab_registry_secret_arn
}
