#!/bin/bash
set -e

# Terraform validation script
# This script validates Terraform configurations without requiring AWS credentials

echo "🔍 Validating Terraform configurations..."

# Check if Terraform is installed
if ! command -v terraform &> /dev/null; then
    echo "❌ Terraform is not installed"
    exit 1
fi

echo "✅ Terraform version: $(terraform version -json | jq -r '.terraform_version')"

# Validate main configuration
echo "🔧 Validating main configuration..."
cd terraform
terraform init -backend=false
terraform validate
echo "✅ Main configuration is valid"

# Validate development environment
echo "🔧 Validating development environment..."
cd environments/development
terraform init -backend=false
terraform validate
echo "✅ Development environment is valid"

# Validate production environment
echo "🔧 Validating production environment..."
cd ../production
terraform init -backend=false
terraform validate
echo "✅ Production environment is valid"

echo "🎉 All Terraform configurations are valid!"
