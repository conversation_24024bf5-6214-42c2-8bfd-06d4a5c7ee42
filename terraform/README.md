# Simplified Terraform Infrastructure for AWS ECS Demo

This directory contains **simplified** Terraform configurations for deploying the NestJS hello-world application to AWS ECS with minimal infrastructure complexity.

## Architecture Overview

The simplified Terraform configuration creates:

- **Default VPC Usage** - leverages existing AWS default networking
- **ECS Fargate Cluster** for serverless container orchestration
- **ECS Service** with basic configuration
- **Security Groups** with HTTP access on port 3000
- **CloudWatch Logs** for application monitoring (3-day retention)
- **IAM Roles** for ECS task execution with GitLab registry access
- **AWS Secrets Manager** for GitLab registry authentication

## Directory Structure

```
terraform/
├── main.tf                    # Main infrastructure resources
├── variables.tf               # Input variables
├── outputs.tf                 # Output values
├── versions.tf                # Provider versions and backend config
├── local-plan.sh              # Local testing script
├── LOCAL_TESTING.md           # Local testing guide
├── environments/
│   ├── development/           # Development environment
│   │   ├── main.tf
│   │   └── terraform.tfvars.example
│   └── production/            # Production environment
│       ├── main.tf
│       └── terraform.tfvars.example
├── scripts/
│   └── terraform-init.sh      # GitLab CI initialization script
└── README.md                  # This file
```

## Environment Configurations

### Development Environment
- **Network**: AWS default VPC and subnets
- **Container Resources**: 256 CPU, 512 MB memory
- **Desired Count**: 1 instance
- **Access**: Public IP assignment for direct access

### Production Environment
- **Network**: AWS default VPC and subnets
- **Container Resources**: 512 CPU, 1024 MB memory
- **Desired Count**: 2 instances
- **Access**: Public IP assignment for direct access

## GitLab CI/CD Integration

### Remote State Management

The configuration uses GitLab's managed Terraform state with HTTP backend:

```bash
# Development state
https://gitlab.uastage.com/api/v4/projects/PROJECT_ID/terraform/state/development

# Production state
https://gitlab.uastage.com/api/v4/projects/PROJECT_ID/terraform/state/production
```

### Pipeline Stages

1. **Plan Stage**: `terraform plan` with container image variable
2. **Deploy Stage**: `terraform apply` with automatic approval

### Required CI/CD Variables

Configure these variables in GitLab CI/CD settings (`Settings > CI/CD > Variables`):

#### AWS Credentials (Required)
- `AWS_ACCESS_KEY_ID`: AWS access key for Terraform
- `AWS_SECRET_ACCESS_KEY`: AWS secret key for Terraform
- `AWS_DEFAULT_REGION`: AWS region (default: us-east-1)

#### GitLab Registry Authentication (Required)
- `CI_REGISTRY_USER`: GitLab registry username (automatically provided)
- `CI_REGISTRY_PASSWORD`: GitLab registry password (automatically provided)

## Local Development

### Quick Local Testing

For rapid local testing without GitLab CI/CD:

```bash
# Copy example variables and edit with your values
cd terraform/environments/development
cp terraform.tfvars.example terraform.tfvars
# Edit terraform.tfvars with your container image and credentials

# Run local plan
cd ../..
./local-plan.sh development
```

See [LOCAL_TESTING.md](./LOCAL_TESTING.md) for detailed local testing instructions.

### Prerequisites

- Terraform >= 1.0
- AWS CLI configured
- GitLab access token with API permissions

### Manual Deployment with GitLab Backend

1. **Initialize Terraform**:
   ```bash
   cd terraform/environments/development
   terraform init \
     -backend-config="address=https://gitlab.uastage.com/api/v4/projects/PROJECT_ID/terraform/state/development" \
     -backend-config="lock_address=https://gitlab.uastage.com/api/v4/projects/PROJECT_ID/terraform/state/development/lock" \
     -backend-config="unlock_address=https://gitlab.uastage.com/api/v4/projects/PROJECT_ID/terraform/state/development/lock" \
     -backend-config="username=YOUR_USERNAME" \
     -backend-config="password=YOUR_ACCESS_TOKEN" \
     -backend-config="lock_method=POST" \
     -backend-config="unlock_method=DELETE"
   ```

2. **Plan Infrastructure**:
   ```bash
   terraform plan \
     -var="container_image=registry.gitlab.com/sw-web/aws-ecs-demo:latest" \
     -var="gitlab_registry_user=YOUR_GITLAB_USER" \
     -var="gitlab_registry_password=YOUR_GITLAB_TOKEN"
   ```

3. **Apply Changes**:
   ```bash
   terraform apply
   ```

### Outputs

After successful deployment, Terraform provides:

- `application_url`: Note about checking ECS console for public IP
- `deployed_image`: Container image that was deployed
- `ecs_cluster_name`: ECS cluster name
- `ecs_service_name`: ECS service name
- `gitlab_registry_secret_arn`: ARN of GitLab registry authentication secret

## Security Considerations

- **Network Access**: ECS tasks run in default VPC with public IP (demo purposes)
- **Security Groups**: Basic HTTP access on port 3000
- **IAM Roles**: ECS task execution with GitLab registry access
- **GitLab Registry Auth**: Secure credential storage in AWS Secrets Manager
- **Container Security**: Non-root user in Docker image

## Monitoring and Logging

- **CloudWatch Logs**: Application logs with 3-day retention
- **ECS Console**: Manual monitoring via AWS console
- **Container Status**: Basic ECS service health monitoring
- **No Health Checks**: Simplified configuration for demo

## Scaling Configuration

### Development
- **Desired Count**: 1 task
- **No Auto-scaling**: Fixed capacity for simplicity
- **Public Access**: Direct IP access via ECS console

### Production
- **Desired Count**: 2 tasks
- **No Auto-scaling**: Fixed capacity for simplicity
- **Public Access**: Direct IP access via ECS console
- **Multi-AZ**: Tasks distributed across default subnets

## Troubleshooting

### Common Issues

1. **State Lock Conflicts**:
   ```bash
   # Force unlock if needed (use with caution)
   terraform force-unlock LOCK_ID
   ```

2. **AWS Credentials**:
   ```bash
   # Verify AWS access
   aws sts get-caller-identity
   ```

3. **Container Image Issues**:
   - Ensure image exists in GitLab registry
   - Check image tag format: `registry.gitlab.com/sw-web/aws-ecs-demo:TAG`
   - Verify GitLab registry credentials are correct

4. **GitLab State Access**:
   - Verify project permissions
   - Check GitLab access token scope

5. **GitLab Registry Authentication**:
   - Check AWS Secrets Manager for stored credentials
   - Verify ECS task has permission to access secrets

### Logs and Debugging

- **ECS Service Events**: Check ECS console for service events
- **CloudWatch Logs**: `/ecs/aws-ecs-demo-{environment}` log group
- **Task Public IP**: Check ECS console for task public IP address
- **Terraform Debug**: Set `TF_LOG=DEBUG` for verbose output

## Cost Optimization

### Development Environment
- **Fargate**: ~$15-20/month for 1 task
- **CloudWatch Logs**: ~$1-2/month (3-day retention)
- **Secrets Manager**: ~$0.40/month
- **Total**: ~$16-23/month

### Production Environment
- **Fargate**: ~$30-40/month for 2 tasks
- **CloudWatch Logs**: ~$2-3/month (3-day retention)
- **Secrets Manager**: ~$0.40/month
- **Total**: ~$32-44/month

### Cost Reduction Benefits
- **No ALB**: Saves ~$16/month per environment
- **No NAT Gateway**: Saves ~$32-64/month per environment
- **Short Log Retention**: Minimal CloudWatch costs
- **Default VPC**: No additional networking costs

**Total Savings**: ~$50-80/month per environment compared to full setup
