import { Injectable } from '@nestjs/common';

@Injectable()
export class AppService {
  getHello(): { message: string; processId: number; timestamp: string } {
    return {
      message: 'Hello World from NestJS!',
      processId: process.pid,
      timestamp: new Date().toISOString(),
    };
  }

  getProcessId(): { processId: number; timestamp: string } {
    return {
      processId: process.pid,
      timestamp: new Date().toISOString(),
    };
  }

  getHealth(): { status: string; processId: number; timestamp: string } {
    return {
      status: 'OK',
      processId: process.pid,
      timestamp: new Date().toISOString(),
    };
  }
}
