import { Controller, Get } from '@nestjs/common';
import { AppService } from './app.service';

@Controller('api')
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get('hello')
  getHello(): { message: string; processId: number; timestamp: string } {
    return this.appService.getHello();
  }

  @Get('process-id')
  getProcessId(): { processId: number; timestamp: string } {
    return this.appService.getProcessId();
  }

  @Get('health')
  getHealth(): { status: string; processId: number; timestamp: string } {
    return this.appService.getHealth();
  }
}
