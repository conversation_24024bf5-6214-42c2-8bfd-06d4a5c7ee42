<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AWS ECS Demo - NestJS App</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .button-group {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .response {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            min-height: 100px;
        }
        .loading {
            color: #007bff;
            font-style: italic;
        }
        .error {
            color: #dc3545;
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        .success {
            color: #155724;
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .info-box {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .process-id {
            font-weight: bold;
            color: #007bff;
            font-size: 18px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 AWS ECS Demo - NestJS Application</h1>
        
        <div class="info-box">
            <h3>Welcome to the NestJS Hello World Demo!</h3>
            <p>This application demonstrates a simple NestJS server running in a containerized environment. 
               Use the buttons below to interact with the API and retrieve the server's process ID.</p>
        </div>

        <div class="button-group">
            <button onclick="fetchHello()">Get Hello Message</button>
            <button onclick="fetchProcessId()">Get Process ID</button>
            <button onclick="fetchHealth()">Health Check</button>
            <button onclick="clearResponse()">Clear Response</button>
        </div>

        <div id="response" class="response">
            Click any button above to interact with the NestJS API...
        </div>
    </div>

    <script>
        const responseDiv = document.getElementById('response');

        function setLoading() {
            responseDiv.className = 'response loading';
            responseDiv.textContent = 'Loading...';
        }

        function setResponse(data, isError = false) {
            responseDiv.className = isError ? 'response error' : 'response success';
            responseDiv.textContent = JSON.stringify(data, null, 2);
        }

        async function makeRequest(endpoint) {
            setLoading();
            try {
                const response = await fetch(`/api/${endpoint}`);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const data = await response.json();
                setResponse(data);
            } catch (error) {
                setResponse({
                    error: 'Failed to fetch data',
                    message: error.message,
                    timestamp: new Date().toISOString()
                }, true);
            }
        }

        function fetchHello() {
            makeRequest('hello');
        }

        function fetchProcessId() {
            makeRequest('process-id');
        }

        function fetchHealth() {
            makeRequest('health');
        }

        function clearResponse() {
            responseDiv.className = 'response';
            responseDiv.textContent = 'Click any button above to interact with the NestJS API...';
        }

        // Auto-fetch process ID on page load
        window.addEventListener('load', () => {
            setTimeout(fetchProcessId, 1000);
        });
    </script>
</body>
</html>
